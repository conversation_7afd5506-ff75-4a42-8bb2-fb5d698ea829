package handlers

import (
	"Wallet/backend/services"
	"net/http"

	"github.com/gin-gonic/gin"
)

// WalletAnalyticsHandler handles wallet analytics data requests
type WalletAnalyticsHandler struct {
	analyticsService *services.WalletAnalyticsService
}

// NewWalletAnalyticsHandler creates a new wallet analytics handler
func NewWalletAnalyticsHandler(analyticsService *services.WalletAnalyticsService) *WalletAnalyticsHandler {
	return &WalletAnalyticsHandler{
		analyticsService: analyticsService,
	}
}

// GetWalletAnalytics returns analytics data for the specified wallet address
func (h *WalletAnalyticsHandler) GetWalletAnalytics(c *gin.Context) {
	address := c.Query("address")
	if address == "" {
		// Check if the address is in the path
		address = c.Param("address")
		if address == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Wallet address is required"})
			return
		}
	}

	// Get analytics for the address
	analytics, err := h.analyticsService.GetWalletAnalytics(address)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get wallet analytics: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, analytics)
}

// GetWalletRiskScore returns a risk score for the wallet based on its analytics
func (h *WalletAnalyticsHandler) GetWalletRiskScore(c *gin.Context) {
	address := c.Query("address")
	if address == "" {
		// Check if the address is in the path
		address = c.Param("address")
		if address == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Wallet address is required"})
			return
		}
	}

	// Get analytics for the address
	analytics, err := h.analyticsService.GetWalletAnalytics(address)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get wallet analytics: " + err.Error()})
		return
	}

	// For now, use a simplified risk score calculation
	// In a real implementation, this would call the ML model
	var riskScore float64
	if analytics.WalletAge < 5 {
		riskScore += 0.4 // New wallets are riskier
	}

	if analytics.SentTxCount < 3 {
		riskScore += 0.3 // Low transaction count is suspicious
	}

	// Cap the risk score at 0.95
	if riskScore > 0.95 {
		riskScore = 0.95
	}

	c.JSON(http.StatusOK, gin.H{
		"address":    address,
		"risk_score": riskScore,
		"risk_level": getRiskLevel(riskScore),
	})
}

// Export wallet data for ML training
func (h *WalletAnalyticsHandler) ExportWalletData(c *gin.Context) {
	// This would be an admin-only endpoint
	// Check authorization

	var req struct {
		Addresses  []string `json:"addresses" binding:"required"`
		OutputPath string   `json:"output_path" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	if err := h.analyticsService.ExportAnalyticsForML(req.Addresses, req.OutputPath); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to export data: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":   "Data exported successfully",
		"file_path": req.OutputPath,
	})
}

// Helper function to get risk level from score
func getRiskLevel(score float64) string {
	if score < 0.2 {
		return "very_low"
	} else if score < 0.4 {
		return "low"
	} else if score < 0.6 {
		return "moderate"
	} else if score < 0.8 {
		return "high"
	} else {
		return "very_high"
	}
}
