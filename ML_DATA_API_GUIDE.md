# ML Analytics Data API Guide

This guide explains how to access wallet analytics data for machine learning model training and prediction.

## 🎯 Exact Field Mapping

**Your ML team requested these fields - here's the exact mapping:**

| **ML Team Request** | **API Field Name** | **Data Type** | **Description** |
|---------------------|-------------------|---------------|-----------------|
| Avg min between sent tnx | `avg_min_between_sent_tx` | float | Average minutes between sent transactions |
| Avg min between received tnx | `avg_min_between_received_tx` | float | Average minutes between received transactions |
| Time Diff between first and last (Mins) | `time_diff_first_last_mins` | float | Time difference between first and last transaction |
| Sent tnx | `sent_tx_count` | int | Total number of sent transactions |
| Received Tnx | `received_tx_count` | int | Total number of received transactions |
| Number of Created Contracts | `created_contracts_count` | int | Number of smart contracts created |
| max value received | `max_value_received` | string (Wei) | Maximum value received in single transaction |
| avg val received | `avg_value_received` | string (Wei) | Average value received per transaction |
| avg val sent | `avg_value_sent` | string (Wei) | Average value sent per transaction |
| total Ether sent | `total_ether_sent` | string (Wei) | Total Ether sent from wallet |
| total ether balance | `total_ether_balance` | string (Wei) | Current Ether balance |
| ERC20 total Ether received | `erc20_total_ether_received` | string (Wei) | Total ERC20 tokens received |
| ERC20 total ether sent | `erc20_total_ether_sent` | string (Wei) | Total ERC20 tokens sent |
| ERC20 total Ether sent contract | `erc20_total_ether_sent_contract` | string (Wei) | ERC20 tokens sent to contracts |
| ERC20 uniq sent addr | `erc20_uniq_sent_addr` | int | Unique addresses sent ERC20 to |
| ERC20 uniq rec token name | `erc20_uniq_rec_token_name` | int | Unique ERC20 token types received |
| ERC20 most sent token type | `erc20_most_sent_token_type` | string | Most frequently sent token symbol |
| ERC20_most_rec_token_type | `erc20_most_rec_token_type` | string | Most frequently received token symbol |

## Available Data Fields

Your system provides all the analytics data fields requested by the ML team:

### Transaction Timing Metrics
- `avg_min_between_sent_tx` - Average minutes between sent transactions
- `avg_min_between_received_tx` - Average minutes between received transactions
- `time_diff_first_last_mins` - Time difference between first and last transaction (minutes)

### Transaction Counts
- `sent_tx_count` - Number of sent transactions
- `received_tx_count` - Number of received transactions
- `created_contracts_count` - Number of created contracts

### ETH Value Metrics
- `max_value_received` - Maximum value received in a single transaction
- `avg_value_received` - Average value received per transaction
- `avg_value_sent` - Average value sent per transaction
- `total_ether_sent` - Total Ether sent
- `total_ether_balance` - Current Ether balance

### ERC20 Token Metrics
- `erc20_total_ether_received` - Total ERC20 tokens received
- `erc20_total_ether_sent` - Total ERC20 tokens sent
- `erc20_total_ether_sent_contract` - Total ERC20 sent to contracts
- `erc20_uniq_sent_addr` - Number of unique addresses sent ERC20 to
- `erc20_uniq_rec_token_name` - Number of unique ERC20 tokens received
- `erc20_most_sent_token_type` - Most frequently sent token type
- `erc20_most_rec_token_type` - Most frequently received token type

### Additional Metrics
- `txn_frequency` - Transaction frequency (transactions per hour)
- `avg_txn_value` - Average transaction value
- `wallet_age_days` - Wallet age in days
- `risk_score` - Calculated risk score

## API Endpoints

### 1. Single Wallet Analytics
Get analytics for a single wallet address.

**Endpoint:** `GET /api/analytics/wallet/{address}`

**Example:**
```bash
curl "http://localhost:8080/api/analytics/wallet/0x742d35Cc6634C0532925a3b8D4C9db96c4b4d8b"
```

**Response:**
```json
{
  "avg_min_between_sent_tx": 120.5,
  "avg_min_between_received_tx": 240.2,
  "time_diff_first_last_mins": 43200,
  "sent_tx_count": 42,
  "received_tx_count": 28,
  "created_contracts_count": 3,
  "max_value_received": "1500000000000000000",
  "avg_value_received": "250000000000000000",
  "avg_value_sent": "180000000000000000",
  "total_ether_sent": "5400000000000000000",
  "total_ether_balance": "2800000000000000000",
  "erc20_total_ether_received": "1000000000000000000",
  "erc20_total_ether_sent": "800000000000000000",
  "erc20_total_ether_sent_contract": "200000000000000000",
  "erc20_uniq_sent_addr": 15,
  "erc20_uniq_rec_token_name": 8,
  "erc20_most_sent_token_type": "USDC",
  "erc20_most_rec_token_type": "USDT",
  "txn_frequency": 0.58,
  "avg_txn_value": "220000000000000000",
  "wallet_age_days": 365,
  "risk_score": 0.25
}
```

### 2. Bulk Analytics (JSON Format)
Get analytics for multiple wallet addresses in JSON format.

**Endpoint:** `POST /api/analytics/bulk`

**Request Body:**
```json
{
  "addresses": [
    "0x742d35Cc6634C0532925a3b8D4C9db96c4b4d8b",
    "0x8ba1f109551bD432803012645Hac136c22C501",
    "******************************************"
  ],
  "format": "json"
}
```

**Response:**
```json
{
  "data": [
    {
      "address": "0x742d35Cc6634C0532925a3b8D4C9db96c4b4d8b",
      "avg_min_between_sent_tx": 120.5,
      "avg_min_between_received_tx": 240.2,
      // ... all other fields
    },
    {
      "address": "0x8ba1f109551bD432803012645Hac136c22C501",
      // ... analytics data
    }
  ],
  "count": 2,
  "errors": [],
  "success": true
}
```

### 3. Bulk Analytics (CSV Format)
Get analytics for multiple wallet addresses in CSV format.

**Endpoint:** `POST /api/analytics/bulk`

**Request Body:**
```json
{
  "addresses": [
    "0x742d35Cc6634C0532925a3b8D4C9db96c4b4d8b",
    "0x8ba1f109551bD432803012645Hac136c22C501"
  ],
  "format": "csv"
}
```

**Response:** CSV file with headers and data rows

### 4. ML Dataset Export
Export wallet analytics as a downloadable CSV file for ML training.

**Endpoint:** `POST /api/analytics/export`

**Request Body:**
```json
{
  "addresses": [
    "0x742d35Cc6634C0532925a3b8D4C9db96c4b4d8b",
    "0x8ba1f109551bD432803012645Hac136c22C501",
    // ... up to 1000 addresses
  ],
  "filename": "training_data_2024.csv"
}
```

**Response:** CSV file download with Content-Disposition header

## Usage Examples

### Python Example for ML Data Collection

```python
import requests
import pandas as pd
import json

# Configuration
API_BASE = "http://localhost:8080/api"

def get_single_wallet_analytics(address):
    """Get analytics for a single wallet"""
    response = requests.get(f"{API_BASE}/analytics/wallet/{address}")
    return response.json()

def get_bulk_analytics_json(addresses):
    """Get analytics for multiple wallets in JSON format"""
    payload = {
        "addresses": addresses,
        "format": "json"
    }
    response = requests.post(f"{API_BASE}/analytics/bulk", json=payload)
    return response.json()

def get_bulk_analytics_csv(addresses):
    """Get analytics for multiple wallets in CSV format"""
    payload = {
        "addresses": addresses,
        "format": "csv"
    }
    response = requests.post(f"{API_BASE}/analytics/bulk", json=payload)
    return response.text

def export_ml_dataset(addresses, filename="ml_dataset.csv"):
    """Export analytics as downloadable CSV"""
    payload = {
        "addresses": addresses,
        "filename": filename
    }
    response = requests.post(f"{API_BASE}/analytics/export", json=payload)

    # Save the CSV file
    with open(filename, 'w') as f:
        f.write(response.text)

    return filename

# Example usage
addresses = [
    "0x742d35Cc6634C0532925a3b8D4C9db96c4b4d8b",
    "0x8ba1f109551bD432803012645Hac136c22C501"
]

# Get data in JSON format
json_data = get_bulk_analytics_json(addresses)
df = pd.DataFrame(json_data['data'])

# Or get data directly as CSV
csv_data = get_bulk_analytics_csv(addresses)
df = pd.read_csv(StringIO(csv_data))

# Export large dataset
large_addresses = ["0x..." for _ in range(1000)]  # Your address list
export_ml_dataset(large_addresses, "training_dataset.csv")
```

### JavaScript/Node.js Example

```javascript
const axios = require('axios');
const fs = require('fs');

const API_BASE = 'http://localhost:8080/api';

async function getBulkAnalytics(addresses, format = 'json') {
    try {
        const response = await axios.post(`${API_BASE}/analytics/bulk`, {
            addresses: addresses,
            format: format
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching analytics:', error);
        throw error;
    }
}

async function exportMLDataset(addresses, filename = 'ml_dataset.csv') {
    try {
        const response = await axios.post(`${API_BASE}/analytics/export`, {
            addresses: addresses,
            filename: filename
        }, {
            responseType: 'text'
        });

        fs.writeFileSync(filename, response.data);
        return filename;
    } catch (error) {
        console.error('Error exporting dataset:', error);
        throw error;
    }
}

// Example usage
const addresses = [
    '0x742d35Cc6634C0532925a3b8D4C9db96c4b4d8b',
    '0x8ba1f109551bD432803012645Hac136c22C501'
];

getBulkAnalytics(addresses, 'json')
    .then(data => {
        console.log(`Retrieved analytics for ${data.count} wallets`);
        console.log('Sample data:', data.data[0]);
    });
```

## Rate Limits and Best Practices

1. **Batch Size**: Maximum 1000 addresses per request
2. **Rate Limiting**: API has rate limiting enabled
3. **Error Handling**: Check the `errors` array in bulk responses
4. **Data Validation**: Validate wallet addresses before sending requests
5. **Caching**: Consider caching results for frequently accessed wallets

## Data Format Notes

- **Ether Values**: Returned as strings in Wei (1 ETH = 10^18 Wei)
- **Timestamps**: All time values are in minutes
- **Token Types**: ERC20 token symbols (e.g., "USDC", "USDT")
- **Risk Scores**: Float values between 0.0 (low risk) and 1.0 (high risk)

## Error Handling

The API returns appropriate HTTP status codes:
- `200`: Success
- `400`: Bad request (invalid addresses, missing parameters)
- `500`: Internal server error

For bulk requests, individual address failures are reported in the `errors` array while successful results are in the `data` array.
